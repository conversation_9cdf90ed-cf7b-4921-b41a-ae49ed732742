#!/bin/bash

echo "🚀 Starting Google CLI & Gemini Docker Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it first."
    echo "📝 Copy .env.example to .env and fill in your API keys"
    exit 1
fi

# Create directories if they don't exist
mkdir -p credentials scripts workspace notebooks

# Start the services
echo "📦 Starting Google CLI containers..."
docker-compose up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 15

# Check if containers are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Google CLI environment is running successfully!"
    echo ""
    echo "🔗 Services:"
    echo "  - Google CLI Container: docker exec -it google-cli bash"
    echo "  - Jupyter Notebook: http://localhost:8888 (token: gemini123)"
    echo ""
    echo "📋 Useful commands:"
    echo "  - Test Gemini: docker exec google-cli python3 /app/scripts/test-gemini.py"
    echo "  - Access CLI: docker exec -it google-cli bash"
    echo "  - View logs: docker-compose logs -f"
    echo "  - Stop services: docker-compose down"
    echo ""
    echo "🔧 Setup steps:"
    echo "  1. Add your GEMINI_API_KEY to .env file"
    echo "  2. Place service account JSON in credentials/ folder"
    echo "  3. Run: docker exec google-cli gcloud auth login"
    echo ""
    
    # Test if API key is configured
    if grep -q "your-gemini-api-key-here" .env; then
        echo "⚠️  Warning: Please update your API keys in .env file"
    else
        echo "🧪 Testing Gemini API..."
        docker exec google-cli python3 /app/scripts/test-gemini.py
    fi
    
else
    echo "❌ Failed to start Google CLI environment. Check the logs:"
    docker-compose logs
fi
