#!/bin/bash

echo "🚀 Starting Gemini CLI Docker Environment..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it first."
    echo "📝 Add your GEMINI_API_KEY to .env file"
    exit 1
fi

# Create directories if they don't exist
mkdir -p scripts workspace config

# Start the services
echo "📦 Starting Gemini CLI container..."
docker-compose up -d

# Wait for services to start
echo "⏳ Waiting for container to start..."
sleep 10

# Check if container is running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Gemini CLI environment is running successfully!"
    echo ""
    echo "🔗 Container Access:"
    echo "  - Gemini CLI: docker exec -it gemini-cli bash"
    echo "  - Helper Script: docker exec gemini-cli node /app/scripts/gemini-helper.js"
    echo ""
    echo "📋 Useful commands:"
    echo "  - Check environment: docker exec gemini-cli node /app/scripts/gemini-helper.js check"
    echo "  - Install dependencies: docker exec gemini-cli node /app/scripts/gemini-helper.js install"
    echo "  - Quick test: docker exec gemini-cli node /app/scripts/gemini-helper.js test"
    echo "  - Run gemini-cli: docker exec gemini-cli node /app/scripts/gemini-helper.js run --help"
    echo ""
    echo "🔧 Direct gemini-cli usage:"
    echo "  - Enter container: docker exec -it gemini-cli bash"
    echo "  - Go to gemini-cli: cd /app/gemini-cli-local"
    echo "  - Run: node index.js --prompt 'your question here'"
    echo ""
    echo "📁 Directories:"
    echo "  - Local gemini-cli: /app/gemini-cli-local"
    echo "  - Workspace: /app/workspace"
    echo "  - Scripts: /app/scripts"
    echo ""

    # Test if API key is configured
    if grep -q "your-gemini-api-key-here" .env; then
        echo "⚠️  Warning: Please update your GEMINI_API_KEY in .env file"
    else
        echo "🧪 Testing environment..."
        docker exec gemini-cli node /app/scripts/gemini-helper.js check
    fi

else
    echo "❌ Failed to start Gemini CLI environment. Check the logs:"
    docker-compose logs
fi
