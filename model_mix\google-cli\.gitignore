# Environment variables
.env
.env.local
.env.production

# Google Cloud credentials
credentials/
*.json
service-account*.json

# API Keys
*api-key*
*secret*

# Docker volumes
gcloud_config/

# Jupyter Notebook checkpoints
.ipynb_checkpoints/
notebooks/.ipynb_checkpoints/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Workspace files
workspace/*.json
workspace/*.log
workspace/temp/

# Logs
*.log
logs/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
