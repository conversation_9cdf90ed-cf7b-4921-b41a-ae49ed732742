# 🤖 Gemini CLI Docker Environment

بيئة Docker للعمل مع Gemini CLI المحلي من Google مع أدوات مساعدة إضافية.

## 📋 المكونات

- **🧠 Gemini CLI**: أداة سطر الأوامر الرسمية من Google
- **🐳 Docker Container**: بيئة معزولة ومحسنة
- **📝 Helper Scripts**: سكريبتات مساعدة للتشغيل والاختبار
- **🔧 Auto-Detection**: اكتشاف تلقائي لمسار gemini-cli المحلي

## 🚀 التشغيل السريع

### 1. إعداد المفاتيح
```bash
# نسخ ملف البيئة
cp .env.example .env

# تعديل المفاتيح
nano .env
```

### 2. تشغيل البيئة
```bash
# تشغيل تلقائي
chmod +x start.sh
./start.sh

# أو تشغيل يدوي
docker-compose up -d
```

### 3. الوصول إلى الخدمات
- **🖥️ Google CLI**: `docker exec -it google-cli bash`
- **📓 Jupyter**: http://localhost:8888 (token: gemini123)

## 🔑 إعداد API Keys

### الحصول على Gemini API Key:
1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. قم بإنشاء مفتاح API جديد
3. انسخ المفتاح إلى ملف `.env`

```env
GEMINI_API_KEY=your-actual-api-key-here
```

## 🧪 اختبار Gemini

### اختبار سريع:
```bash
docker exec google-cli python3 /app/scripts/test-gemini.py
```

### اختبار تفاعلي:
```bash
# الدخول إلى الحاوية
docker exec -it google-cli bash

# تشغيل Python
python3
>>> import google.generativeai as genai
>>> genai.configure(api_key="your-key")
>>> model = genai.GenerativeModel('gemini-pro')
>>> response = model.generate_content("مرحبا")
>>> print(response.text)
```

## 📁 بنية المشروع

```
google-cli/
├── docker-compose.yml      # تكوين الحاويات
├── .env                   # المتغيرات البيئية
├── start.sh              # سكريبت التشغيل
├── credentials/          # ملفات الاعتماد
├── scripts/             # سكريبتات Python
│   └── test-gemini.py   # اختبار Gemini
├── workspace/           # مساحة العمل
├── notebooks/           # Jupyter Notebooks
│   └── gemini-test.ipynb
└── README.md           # هذا الملف
```

## 🛠️ الأوامر المفيدة

### إدارة الحاويات:
```bash
# عرض الحالة
docker-compose ps

# عرض السجلات
docker-compose logs -f

# إيقاف الخدمات
docker-compose down

# إعادة تشغيل
docker-compose restart
```

### استخدام Google Cloud CLI:
```bash
# تسجيل الدخول
docker exec -it google-cli gcloud auth login

# إعداد المشروع
docker exec google-cli gcloud config set project YOUR_PROJECT_ID

# عرض المعلومات
docker exec google-cli gcloud info
```

### استخدام Gemini API:
```bash
# اختبار النماذج المتاحة
docker exec google-cli python3 -c "
import google.generativeai as genai
genai.configure(api_key='YOUR_KEY')
for m in genai.list_models():
    print(m.name)
"
```

## 🔧 التكوين المتقدم

### إضافة Service Account:
1. إنشاء Service Account في Google Cloud Console
2. تحميل ملف JSON
3. وضعه في مجلد `credentials/`
4. تعيين المتغير في `.env`:
```env
GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json
```

### تخصيص Jupyter:
```env
JUPYTER_TOKEN=your-custom-token
```

## 📊 أمثلة الاستخدام

### مثال 1: توليد النص
```python
import google.generativeai as genai

genai.configure(api_key="your-key")
model = genai.GenerativeModel('gemini-pro')

response = model.generate_content("اكتب قصة قصيرة")
print(response.text)
```

### مثال 2: المحادثة التفاعلية
```python
chat = model.start_chat(history=[])

response1 = chat.send_message("مرحبا")
response2 = chat.send_message("ما اسمك؟")
```

### مثال 3: إعدادات متقدمة
```python
from google.generativeai.types import HarmCategory, HarmBlockThreshold

generation_config = {
    "temperature": 0.9,
    "top_p": 1,
    "top_k": 1,
    "max_output_tokens": 2048,
}

safety_settings = {
    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
}

model = genai.GenerativeModel(
    model_name="gemini-pro",
    generation_config=generation_config,
    safety_settings=safety_settings
)
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

#### API Key غير صحيح:
```
❌ Error: Invalid API key
✅ تحقق من GEMINI_API_KEY في .env
```

#### مشكلة في الاتصال:
```bash
# اختبار الاتصال
docker exec google-cli curl -s https://generativelanguage.googleapis.com/v1/models?key=YOUR_KEY
```

#### Jupyter لا يعمل:
```bash
# إعادة تشغيل Jupyter
docker-compose restart jupyter
```

## 🔒 الأمان

⚠️ **مهم للإنتاج**:
1. لا تضع API Keys في الكود
2. استخدم متغيرات البيئة
3. قيد الوصول للمنافذ
4. استخدم HTTPS

## 📚 موارد إضافية

- [Google AI Studio](https://makersuite.google.com/)
- [Gemini API Documentation](https://ai.google.dev/)
- [Google Cloud CLI](https://cloud.google.com/sdk/gcloud)

---

**✅ البيئة جاهزة للاستخدام!**

- Google CLI: `docker exec -it google-cli bash`
- Jupyter: http://localhost:8888
- Test Script: `docker exec google-cli python3 /app/scripts/test-gemini.py`
