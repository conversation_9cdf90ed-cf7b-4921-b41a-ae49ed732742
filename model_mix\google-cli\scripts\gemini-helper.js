#!/usr/bin/env node
/**
 * مساعد للعمل مع Gemini CLI
 * Gemini CLI Helper Script
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// تحميل متغيرات البيئة
require('dotenv').config();

class GeminiHelper {
    constructor() {
        this.apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
        this.geminiCliPath = '/app/gemini-cli-local';
        this.workspacePath = '/app/workspace';
    }

    /**
     * التحقق من إعداد البيئة
     */
    checkEnvironment() {
        console.log('🔍 التحقق من البيئة...');
        
        // التحقق من API Key
        if (!this.apiKey) {
            console.log('❌ GEMINI_API_KEY غير محدد');
            return false;
        }
        console.log('✅ API Key محدد');

        // التحقق من gemini-cli
        if (!fs.existsSync(this.geminiCliPath)) {
            console.log('❌ gemini-cli غير موجود في:', this.geminiCliPath);
            return false;
        }
        console.log('✅ gemini-cli موجود');

        // التحقق من package.json
        const packagePath = path.join(this.geminiCliPath, 'package.json');
        if (!fs.existsSync(packagePath)) {
            console.log('❌ package.json غير موجود');
            return false;
        }
        console.log('✅ package.json موجود');

        return true;
    }

    /**
     * تثبيت dependencies لـ gemini-cli
     */
    installDependencies() {
        console.log('📦 تثبيت dependencies...');
        
        try {
            process.chdir(this.geminiCliPath);
            execSync('npm install', { stdio: 'inherit' });
            console.log('✅ تم تثبيت dependencies بنجاح');
            return true;
        } catch (error) {
            console.log('❌ فشل في تثبيت dependencies:', error.message);
            return false;
        }
    }

    /**
     * تشغيل gemini-cli مع معاملات
     */
    runGeminiCli(args = []) {
        console.log('🚀 تشغيل gemini-cli...');
        
        try {
            process.chdir(this.geminiCliPath);
            
            // إعداد متغيرات البيئة
            const env = {
                ...process.env,
                GEMINI_API_KEY: this.apiKey
            };

            const command = `node index.js ${args.join(' ')}`;
            console.log('📝 الأمر:', command);
            
            execSync(command, { 
                stdio: 'inherit',
                env: env
            });
            
            return true;
        } catch (error) {
            console.log('❌ فشل في تشغيل gemini-cli:', error.message);
            return false;
        }
    }

    /**
     * إنشاء ملف تكوين
     */
    createConfig() {
        const configPath = path.join(this.workspacePath, 'gemini-config.json');
        
        const config = {
            apiKey: this.apiKey,
            model: 'gemini-pro',
            temperature: 0.7,
            maxTokens: 1000,
            language: 'ar'
        };

        try {
            fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
            console.log('✅ تم إنشاء ملف التكوين:', configPath);
            return true;
        } catch (error) {
            console.log('❌ فشل في إنشاء ملف التكوين:', error.message);
            return false;
        }
    }

    /**
     * اختبار سريع
     */
    quickTest() {
        console.log('🧪 اختبار سريع...');
        
        const testPrompt = 'قل مرحبا باللغة العربية';
        return this.runGeminiCli(['--prompt', `"${testPrompt}"`]);
    }

    /**
     * عرض المساعدة
     */
    showHelp() {
        console.log(`
🤖 Gemini CLI Helper

الاستخدام:
  node gemini-helper.js [command]

الأوامر:
  check       - التحقق من البيئة
  install     - تثبيت dependencies
  config      - إنشاء ملف تكوين
  test        - اختبار سريع
  run [args]  - تشغيل gemini-cli مع معاملات
  help        - عرض هذه المساعدة

أمثلة:
  node gemini-helper.js check
  node gemini-helper.js test
  node gemini-helper.js run --prompt "ما هو الذكاء الاصطناعي؟"
        `);
    }
}

// تشغيل المساعد
function main() {
    const helper = new GeminiHelper();
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
        case 'check':
            helper.checkEnvironment();
            break;
        
        case 'install':
            if (helper.checkEnvironment()) {
                helper.installDependencies();
            }
            break;
        
        case 'config':
            helper.createConfig();
            break;
        
        case 'test':
            if (helper.checkEnvironment()) {
                helper.quickTest();
            }
            break;
        
        case 'run':
            if (helper.checkEnvironment()) {
                const runArgs = args.slice(1);
                helper.runGeminiCli(runArgs);
            }
            break;
        
        case 'help':
        default:
            helper.showHelp();
            break;
    }
}

if (require.main === module) {
    main();
}

module.exports = GeminiHelper;
