{"name": "AI Collaborative Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ai-coordinator", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "ai-coordinator-webhook"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.body.complexity}}", "operation": "equal", "value2": "high"}]}}, "id": "decision-node", "name": "Decision Engine", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "http://localhost:3333/api/coordinate", "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{$json.body.prompt}}"}, {"name": "options", "value": "={{$json.body.options}}"}]}, "options": {"response": {"response": {"responseFormat": "json"}}}}, "id": "coordinator-call", "name": "AI Coordinator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 180]}, {"parameters": {"url": "http://localhost:3333/api/collaborate", "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{$json.body.prompt}}"}]}, "options": {"response": {"response": {"responseFormat": "json"}}}}, "id": "collaborative-call", "name": "Collaborative AI", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 420]}, {"parameters": {"respondWith": "json", "responseBody": "={{$json}}"}, "id": "response-node", "name": "Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Decision Engine", "type": "main", "index": 0}]]}, "Decision Engine": {"main": [[{"node": "AI Coordinator", "type": "main", "index": 0}], [{"node": "Collaborative AI", "type": "main", "index": 0}]]}, "AI Coordinator": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}, "Collaborative AI": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1", "meta": {"instanceId": "ai-collaborative-workspace"}}