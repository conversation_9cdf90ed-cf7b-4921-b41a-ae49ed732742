FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache \
    git \
    bash \
    curl \
    wget \
    jq \
    python3 \
    py3-pip \
    nano \
    vim

# Set working directory
WORKDIR /app

# Install global npm packages
RUN npm install -g @google/generative-ai

# Create directories
RUN mkdir -p /app/gemini-cli /app/workspace /app/scripts

# Copy local gemini-cli if mounted
COPY ./gemini-cli-local /app/gemini-cli-local

# Install Python dependencies for additional tools
RUN pip3 install --no-cache-dir \
    google-generativeai \
    google-cloud-aiplatform \
    requests \
    python-dotenv

# Create a startup script
RUN echo '#!/bin/bash' > /app/start.sh && \
    echo 'echo "🚀 Gemini CLI Docker Environment"' >> /app/start.sh && \
    echo 'echo "================================"' >> /app/start.sh && \
    echo 'echo "📁 Gemini CLI: /app/gemini-cli-local"' >> /app/start.sh && \
    echo 'echo "📁 Workspace: /app/workspace"' >> /app/start.sh && \
    echo 'echo "🔑 API Key: \$GEMINI_API_KEY"' >> /app/start.sh && \
    echo 'echo ""' >> /app/start.sh && \
    echo 'if [ -d "/app/gemini-cli-local" ]; then' >> /app/start.sh && \
    echo '  echo "✅ Local gemini-cli found"' >> /app/start.sh && \
    echo '  cd /app/gemini-cli-local' >> /app/start.sh && \
    echo '  if [ -f "package.json" ]; then' >> /app/start.sh && \
    echo '    echo "📦 Installing dependencies..."' >> /app/start.sh && \
    echo '    npm install' >> /app/start.sh && \
    echo '  fi' >> /app/start.sh && \
    echo 'else' >> /app/start.sh && \
    echo '  echo "⚠️  Local gemini-cli not mounted"' >> /app/start.sh && \
    echo '  echo "💡 Mount your local gemini-cli to /app/gemini-cli-local"' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo 'echo ""' >> /app/start.sh && \
    echo 'echo "🎯 Ready to use Gemini CLI!"' >> /app/start.sh && \
    echo 'exec "$@"' >> /app/start.sh && \
    chmod +x /app/start.sh

# Set default command
CMD ["/app/start.sh", "tail", "-f", "/dev/null"]
