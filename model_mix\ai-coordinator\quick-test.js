const axios = require('axios');

async function quickTest() {
  console.log('🧪 Quick Test for AI Coordinator\n');
  
  try {
    // Test Ollama directly first
    console.log('1️⃣ Testing Ollama directly...');
    const ollamaResponse = await axios.post('http://localhost:11434/api/generate', {
      model: 'phi3:mini',
      prompt: 'Say hello in Arabic',
      stream: false
    });
    console.log('✅ Ollama works:', ollamaResponse.data.response.substring(0, 50) + '...');
    
  } catch (error) {
    console.log('❌ Ollama test failed:', error.message);
  }
  
  try {
    // Test our coordinator
    console.log('\n2️⃣ Testing AI Coordinator...');
    const coordResponse = await axios.post('http://localhost:3333/api/coordinate', {
      prompt: 'Say hello in Arabic',
      options: { priority: 'fast' }
    });
    console.log('✅ Coordinator works:', coordResponse.data);
    
  } catch (error) {
    console.log('❌ Coordinator test failed:', error.message);
  }
}

quickTest();
