version: '3.8'

services:
  google-cli:
    image: google/cloud-sdk:latest
    container_name: google-cli
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      # Google Cloud Configuration
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
      - GCLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
      
      # Gemini API Configuration
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      
      # CLI Configuration
      - CLOUDSDK_CORE_DISABLE_PROMPTS=1
      - CLOUDSDK_PYTHON_SITEPACKAGES=1
      
    volumes:
      # Persist Google Cloud configuration
      - gcloud_config:/root/.config/gcloud
      
      # Mount credentials directory
      - ./credentials:/app/credentials:ro
      
      # Mount scripts and tools
      - ./scripts:/app/scripts
      
      # Mount workspace for projects
      - ./workspace:/app/workspace
      
    working_dir: /app
    
    # Install additional tools
    command: >
      bash -c "
        # Update and install additional packages
        apt-get update && 
        apt-get install -y curl wget jq python3-pip nano vim git &&
        
        # Install Google AI Python SDK
        pip3 install google-generativeai google-cloud-aiplatform &&
        
        # Install additional CLI tools
        pip3 install google-cloud-storage google-cloud-bigquery &&
        
        # Keep container running
        tail -f /dev/null
      "
    
    # Health check
    healthcheck:
      test: ["CMD", "gcloud", "version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Optional: Jupyter Notebook for interactive development
  jupyter:
    image: jupyter/base-notebook:latest
    container_name: google-cli-jupyter
    restart: unless-stopped
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-gemini123}
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./credentials:/home/<USER>/credentials:ro
    command: >
      bash -c "
        pip install google-generativeai google-cloud-aiplatform &&
        start-notebook.sh --NotebookApp.token='${JUPYTER_TOKEN:-gemini123}'
      "
    depends_on:
      - google-cli

volumes:
  gcloud_config:
    driver: local
