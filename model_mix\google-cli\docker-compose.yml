version: '3.8'

services:
  gemini-cli:
    build: .
    container_name: gemini-cli
    restart: unless-stopped
    ports:
      - "3001:3000"
      - "8080:8080"
    environment:
      # Gemini API Configuration
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}

      # Optional: Google Cloud Project
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}

      # Node.js Configuration
      - NODE_ENV=development

    volumes:
      # Mount local gemini-cli repository
      # تعديل المسار حسب موقع gemini-cli على جهازك
      - C:\Users\<USER>\supper gemini:/app/gemini-cli-local

      # Mount workspace for projects
      - ./workspace:/app/workspace

      # Mount scripts directory
      - ./scripts:/app/scripts

      # Mount config directory
      - ./config:/app/config

    working_dir: /app

    # Health check
    healthcheck:
      test: ["CMD", "node", "--version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Optional: Jupyter Notebook for interactive development
  jupyter:
    image: jupyter/base-notebook:latest
    container_name: google-cli-jupyter
    restart: unless-stopped
    ports:
      - "8888:8888"
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-gemini123}
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./credentials:/home/<USER>/credentials:ro
    command: >
      bash -c "
        pip install google-generativeai google-cloud-aiplatform &&
        start-notebook.sh --NotebookApp.token='${JUPYTER_TOKEN:-gemini123}'
      "
    depends_on:
      - google-cli

volumes:
  gcloud_config:
    driver: local
