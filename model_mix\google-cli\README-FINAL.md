# 🤖 Gemini CLI Docker Environment - الدليل الشامل

بيئة Docker محسنة للعمل مع Gemini CLI المحلي من Google مع أدوات مساعدة متقدمة.

## 🎯 نظرة عامة

هذا المشروع يوفر حاوية Docker تتكامل مع تثبيت Gemini CLI المحلي، مما يوفر:
- بيئة عمل معزولة ومحسنة
- اكتشاف تلقائي لمسار gemini-cli
- سكريبتات مساعدة للتشغيل والاختبار
- إدارة سهلة للمشاريع والملفات

## 📦 المتطلبات

### المتطلبات المحلية:
- ✅ **Gemini CLI مثبت محلياً** من: https://github.com/google-gemini/gemini-cli.git
- ✅ **Docker & Docker Compose**
- ✅ **Gemini API Key** من Google AI Studio

### التحقق من التثبيت:
```bash
# التحقق من gemini-cli
ls ~/gemini-cli  # أو المسار الذي ثبتته فيه

# التحقق من Docker
docker --version
docker-compose --version
```

## 🚀 الإعداد السريع

### 1. اكتشاف مسار gemini-cli تلقائياً:
```bash
python find-gemini-cli.py
```

### 2. إعداد API Key:
```bash
# تعديل ملف .env
nano .env

# إضافة مفتاح API
GEMINI_API_KEY=your-actual-api-key-here
```

### 3. تشغيل البيئة:
```bash
# تشغيل تلقائي
chmod +x start.sh
./start.sh

# أو تشغيل يدوي
docker-compose up -d
```

## 🔧 الاستخدام

### الوصول إلى الحاوية:
```bash
# دخول تفاعلي
docker exec -it gemini-cli bash

# تشغيل أمر واحد
docker exec gemini-cli node /app/scripts/gemini-helper.js check
```

### استخدام Helper Script:
```bash
# التحقق من البيئة
docker exec gemini-cli node /app/scripts/gemini-helper.js check

# تثبيت dependencies
docker exec gemini-cli node /app/scripts/gemini-helper.js install

# اختبار سريع
docker exec gemini-cli node /app/scripts/gemini-helper.js test

# تشغيل gemini-cli مع معاملات
docker exec gemini-cli node /app/scripts/gemini-helper.js run --prompt "ما هو الذكاء الاصطناعي؟"
```

### استخدام gemini-cli مباشرة:
```bash
# الدخول إلى الحاوية
docker exec -it gemini-cli bash

# الانتقال إلى مجلد gemini-cli
cd /app/gemini-cli-local

# تشغيل gemini-cli
node index.js --prompt "سؤالك هنا"
node index.js --help  # لعرض جميع الخيارات
```

## 📁 بنية المشروع

```
google-cli/
├── Dockerfile                 # تعريف الحاوية
├── docker-compose.yml         # تكوين الخدمات
├── .env                      # المتغيرات البيئية
├── find-gemini-cli.py        # اكتشاف مسار gemini-cli
├── start.sh                  # سكريبت التشغيل
├── scripts/
│   ├── gemini-helper.js      # مساعد JavaScript
│   └── test-gemini.py        # اختبار Python
├── workspace/                # مساحة العمل
├── config/                   # ملفات التكوين
└── README-FINAL.md          # هذا الملف
```

## 🛠️ الأوامر المفيدة

### إدارة الحاوية:
```bash
# عرض الحالة
docker-compose ps

# عرض السجلات
docker-compose logs -f

# إيقاف الخدمة
docker-compose down

# إعادة تشغيل
docker-compose restart

# إعادة بناء الحاوية
docker-compose build --no-cache
```

### العمل مع الملفات:
```bash
# نسخ ملف إلى الحاوية
docker cp file.txt gemini-cli:/app/workspace/

# نسخ ملف من الحاوية
docker cp gemini-cli:/app/workspace/output.txt ./

# عرض محتويات مجلد العمل
docker exec gemini-cli ls -la /app/workspace/
```

## 🧪 أمثلة الاستخدام

### مثال 1: سؤال بسيط
```bash
docker exec gemini-cli node /app/scripts/gemini-helper.js run --prompt "اشرح الذكاء الاصطناعي"
```

### مثال 2: معالجة ملف
```bash
# نسخ ملف نصي إلى الحاوية
docker cp document.txt gemini-cli:/app/workspace/

# معالجة الملف
docker exec gemini-cli node /app/gemini-cli-local/index.js --file /app/workspace/document.txt --prompt "لخص هذا النص"
```

### مثال 3: حفظ النتائج
```bash
# تشغيل مع حفظ النتيجة
docker exec gemini-cli bash -c "cd /app/gemini-cli-local && node index.js --prompt 'اكتب قصة قصيرة' > /app/workspace/story.txt"

# عرض النتيجة
docker exec gemini-cli cat /app/workspace/story.txt
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. gemini-cli غير موجود:
```bash
# التحقق من المسار
docker exec gemini-cli ls -la /app/gemini-cli-local/

# إعادة تشغيل find-gemini-cli.py
python find-gemini-cli.py
```

#### 2. API Key غير صحيح:
```bash
# التحقق من المتغير
docker exec gemini-cli echo $GEMINI_API_KEY

# تحديث .env وإعادة التشغيل
docker-compose restart
```

#### 3. مشاكل في dependencies:
```bash
# إعادة تثبيت
docker exec gemini-cli node /app/scripts/gemini-helper.js install

# أو يدوياً
docker exec gemini-cli bash -c "cd /app/gemini-cli-local && npm install"
```

#### 4. مشاكل في الأذونات:
```bash
# إصلاح الأذونات
docker exec gemini-cli chmod -R 755 /app/workspace/
```

## 🔒 الأمان

### للاستخدام الآمن:
1. **لا تشارك API Keys**: احتفظ بـ `.env` محلياً
2. **استخدم .gitignore**: تأكد من عدم رفع المفاتيح
3. **قيد الوصول**: استخدم firewall للمنافذ
4. **النسخ الاحتياطي**: احفظ نسخة من مجلد workspace

### ملف .gitignore:
```gitignore
.env
.env.local
*.log
workspace/*.json
config/*.key
```

## 📊 مراقبة الأداء

```bash
# استخدام الموارد
docker stats gemini-cli

# مساحة القرص
docker system df

# سجلات مفصلة
docker-compose logs --tail=100 -f
```

## 🤝 المساهمة

لتحسين هذا المشروع:
1. Fork المستودع
2. أنشئ branch جديد
3. اعمل التحسينات
4. أرسل Pull Request

## 📚 موارد إضافية

- **Gemini CLI**: https://github.com/google-gemini/gemini-cli
- **Google AI Studio**: https://makersuite.google.com/
- **Gemini API Docs**: https://ai.google.dev/
- **Docker Docs**: https://docs.docker.com/

---

## ✅ الحالة الحالية

**🎉 تم الإعداد بنجاح!**

- ✅ تم اكتشاف gemini-cli في: `C:\Users\<USER>\supper gemini`
- ✅ تم تحديث docker-compose.yml
- ✅ الحاوية جاهزة للتشغيل

### الخطوات التالية:
1. أضف `GEMINI_API_KEY` إلى ملف `.env`
2. شغل: `docker-compose up -d`
3. اختبر: `docker exec gemini-cli node /app/scripts/gemini-helper.js test`

**🚀 استمتع بالعمل مع Gemini CLI!**
