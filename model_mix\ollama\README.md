# Ollama Docker Setup

هذا المشروع يحتوي على إعداد Docker لتشغيل Ollama مع واجهة ويب.

## المكونات

- **Ollama**: خادم النماذج اللغوية
- **Open WebUI**: واجهة ويب لـ Ollama

## التشغيل

### 1. تشغيل Ollama

```bash
cd ollama
docker-compose up -d
```

### 2. الوصول إلى الخدمات

- **Ollama API**: http://localhost:11434
- **Web UI**: http://localhost:3000

## إدارة النماذج

### تحميل نموذج جديد
```bash
# الدخول إلى حاوية Ollama
docker exec -it ollama ollama pull llama3:8b

# أو تشغيل نموذج مباشرة (سيتم تحميله تلقائياً)
docker exec -it ollama ollama run llama3:8b
```

### عرض النماذج المثبتة
```bash
docker exec -it ollama ollama list
```

### حذف نموذج
```bash
docker exec -it ollama ollama rm model-name
```

## نسخ النماذج من التثبيت المحلي

إذا كان لديك نماذج مثبتة محلياً وتريد نسخها إلى الحاوية:

```bash
# نسخ نموذج من المجلد المحلي إلى الحاوية
docker cp ~/.ollama/models/. ollama:/root/.ollama/models/
```

## الأوامر المفيدة

### عرض السجلات
```bash
docker-compose logs -f ollama
docker-compose logs -f ollama-webui
```

### إيقاف الخدمات
```bash
docker-compose down
```

### إعادة تشغيل
```bash
docker-compose restart
```

### تحديث الصور
```bash
docker-compose pull
docker-compose up -d
```

## اختبار API

```bash
# اختبار الاتصال
curl http://localhost:11434/api/tags

# تشغيل نموذج
curl http://localhost:11434/api/generate -d '{
  "model": "llama3:8b",
  "prompt": "Why is the sky blue?",
  "stream": false
}'
```

## دعم GPU

إذا كان لديك كرت رسومات NVIDIA:

1. قم بإلغاء التعليق على قسم GPU في `docker-compose.yml`
2. تأكد من تثبيت NVIDIA Container Toolkit
3. أعد تشغيل الخدمة

## استكشاف الأخطاء

### إذا لم يعمل Ollama:
1. تحقق من السجلات: `docker-compose logs ollama`
2. تأكد من أن المنفذ 11434 غير مستخدم
3. تحقق من مساحة القرص الصلب (النماذج تحتاج مساحة كبيرة)

### إذا لم تعمل واجهة الويب:
1. تحقق من السجلات: `docker-compose logs ollama-webui`
2. تأكد من أن المنفذ 3000 غير مستخدم
3. تأكد من تشغيل Ollama أولاً

## الأمان

⚠️ **مهم**: غير `WEBUI_SECRET_KEY` في ملف `.env` قبل الاستخدام في بيئة الإنتاج!
