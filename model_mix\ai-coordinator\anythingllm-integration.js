/**
 * AnythingLLM Integration Functions
 * دوال التكامل مع AnythingLLM
 */

// Function 1: Smart Coordinator - يختار النموذج المناسب تلقائياً
async function smartCoordinator(prompt, options = {}) {
  try {
    const response = await fetch('http://localhost:3333/api/coordinate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ prompt, options })
    });
    
    const result = await response.json();
    
    if (result.success) {
      return `🤖 **[${result.decision.model.toUpperCase()}]** (${result.decision.reason}):\n\n${result.response}`;
    } else {
      return `❌ **خطأ**: ${result.error}`;
    }
  } catch (error) {
    return `❌ **خطأ في الاتصال**: ${error.message}`;
  }
}

// Function 2: Collaborative Response - يحصل على إجابات من عدة نماذج
async function collaborativeAI(prompt) {
  try {
    const response = await fetch('http://localhost:3333/api/collaborate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ prompt })
    });
    
    const result = await response.json();
    
    if (result.success && result.responses) {
      let output = `🤝 **إجابة تعاونية من ${result.responses.length} نماذج:**\n\n`;
      
      result.responses.forEach((resp, index) => {
        output += `### 🤖 ${resp.source.toUpperCase()} (${resp.model}):\n`;
        output += `${resp.response}\n\n`;
        output += `---\n\n`;
      });
      
      return output;
    } else {
      return `❌ **خطأ**: لم يتم الحصول على إجابات`;
    }
  } catch (error) {
    return `❌ **خطأ في الاتصال**: ${error.message}`;
  }
}

// Function 3: Fast Local Response - استجابة سريعة من Ollama فقط
async function fastLocal(prompt) {
  return await smartCoordinator(prompt, { priority: 'fast' });
}

// Function 4: Deep Analysis - تحليل عميق من Gemini
async function deepAnalysis(prompt) {
  return await smartCoordinator(prompt, { complexity: 'high' });
}

// Function 5: Code Helper - مساعد برمجي
async function codeHelper(prompt) {
  const codePrompt = `[CODE CONTEXT] ${prompt}`;
  return await smartCoordinator(codePrompt, { context: 'code', priority: 'fast' });
}

// Function 6: Research Assistant - مساعد بحثي
async function researchAssistant(prompt) {
  const researchPrompt = `[RESEARCH] ${prompt}`;
  return await smartCoordinator(researchPrompt, { complexity: 'high' });
}

// Function 7: Arabic Helper - مساعد عربي
async function arabicHelper(prompt) {
  const arabicPrompt = `يرجى الإجابة باللغة العربية: ${prompt}`;
  return await smartCoordinator(arabicPrompt, { priority: 'normal' });
}

// Function 8: System Status - حالة النظام
async function systemStatus() {
  try {
    const response = await fetch('http://localhost:3333/api/health');
    const result = await response.json();
    
    let status = `📊 **حالة النظام** (${result.timestamp}):\n\n`;
    status += `🔧 **المنسق**: ${result.coordinator}\n`;
    status += `🤖 **Ollama**: ${result.services.ollama}\n`;
    status += `🧠 **Gemini**: ${result.services.gemini}\n\n`;
    
    if (result.services.ollama === 'healthy') {
      status += `✅ جميع الأنظمة تعمل بشكل طبيعي`;
    } else {
      status += `⚠️ بعض الأنظمة تحتاج فحص`;
    }
    
    return status;
  } catch (error) {
    return `❌ **خطأ في فحص النظام**: ${error.message}`;
  }
}

// Export functions for use in AnythingLLM
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    smartCoordinator,
    collaborativeAI,
    fastLocal,
    deepAnalysis,
    codeHelper,
    researchAssistant,
    arabicHelper,
    systemStatus
  };
}

// Instructions for AnythingLLM setup
console.log(`
🔧 **تعليمات التكامل مع AnythingLLM:**

1. **انسخ الدوال التالية إلى AnythingLLM Custom Functions:**

   - smartCoordinator(prompt, options)
   - collaborativeAI(prompt)
   - fastLocal(prompt)
   - deepAnalysis(prompt)
   - codeHelper(prompt)
   - researchAssistant(prompt)
   - arabicHelper(prompt)
   - systemStatus()

2. **أمثلة الاستخدام في AnythingLLM:**

   @smartCoordinator("ما هو الذكاء الاصطناعي؟")
   @collaborativeAI("أفضل لغة برمجة للمبتدئين؟")
   @fastLocal("2+2=?")
   @deepAnalysis("تحليل اتجاهات التكنولوجيا 2024")
   @codeHelper("كيف أنشئ React component؟")
   @researchAssistant("ابحث عن أحدث تطورات الذكاء الاصطناعي")
   @arabicHelper("What is machine learning?")
   @systemStatus()

3. **تأكد من تشغيل AI Coordinator على المنفذ 3333**

4. **استمتع بالتعاون الذكي بين النماذج! 🚀**
`);
